/**
 * AGENTIC AI VIDEO GENERATION - Inngest Function
 *
 * This is a completely separate Inngest function dedicated to the agentic AI approach.
 * It runs in parallel with the traditional workflow function, allowing us to:
 * 1. Test the agentic approach without breaking existing functionality
 * 2. Compare both approaches side by side
 * 3. Add agentic features incrementally
 * 4. Eventually decide which approach to use
 *
 * Current Implementation: Script Generation Agent Only
 * Future Features: Media Selection Agent, Quality Assessment Agent, etc.
 */

import { inngest } from '../client'
import { generateVideoScript } from '@/lib/script-agent'

/**
 * AGENTIC VIDEO GENERATION INNGEST FUNCTION
 *
 * Event Name: 'generate-video-data-from-agent'
 * Purpose: Generate video data using autonomous AI agents
 *
 * This function demonstrates the agentic AI approach where:
 * - AI agents make autonomous decisions
 * - Each agent has a specific responsibility
 * - Agents can collaborate and build on each other's work
 * - The system is extensible and observable
 */
export const generateVideoDataFromAgent = inngest.createFunction(
  {
    id: 'generate-video-data-from-agent',
    name: 'Generate Video Data (Agentic AI)',
    retries: 0,
  },
  { event: 'generate-video-data-from-agent' },
  async ({ event, step }) => {
    // ============================================================================
    // STEP 0: INITIALIZATION & VALIDATION
    // ============================================================================

    // Extract and validate input parameters
    const {
      userId,
      organizationId,
      // Multiple input types support
      idea,
      blogUrl,
      pdfUrl,
      text,
      // Generation parameters
      duration = 30,
      tone = 'friendly',
      audience = 'general',
      platform = 'YouTube',
      language = 'English',
      keywords,
      hook = false,
      callToAction = false,
    } = event.data

    // Validate required fields
    if (!userId) {
      throw new Error('❌ userId is required but was not provided')
    }

    // Flexible input validation - accept any of the supported input types
    const input = idea || blogUrl || pdfUrl || text
    if (!input) {
      throw new Error(
        '❌ At least one input type is required: idea, blogUrl, pdfUrl, or text'
      )
    }

    // Determine input type and prepare for script agent
    let userIdea: string
    let inputType: string

    if (idea) {
      userIdea = idea
      inputType = 'idea'
    } else if (blogUrl) {
      userIdea = blogUrl
      inputType = 'blogUrl'
    } else if (pdfUrl) {
      userIdea = pdfUrl
      inputType = 'pdfUrl'
    } else if (text) {
      userIdea = text
      inputType = 'text'
    } else {
      // This should never happen due to validation above, but TypeScript safety
      throw new Error('❌ No valid input provided')
    }

    // ============================================================================
    // STEP 1: SCRIPT GENERATION AGENT
    // ============================================================================

    const scriptResult = await step.run('script-generation-agent', async () => {
      try {
        // Call our LangGraph script generation agent with all parameters
        // The agent will automatically detect input type and route accordingly
        const startTime = Date.now()
        const generatedScript = await generateVideoScript({
          userIdea, // Uses the determined input (idea, blogUrl, pdfUrl, or text)
          duration,
          tone,
          audience,
          platform,
          language,
          keywords,
          hook,
          callToAction,
        })
        const endTime = Date.now()

        return {
          success: true,
          script: generatedScript,
          executionTime: endTime - startTime,
          agentType: 'script-generation',
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          agentType: 'script-generation',
        }
      }
    })

    // Check if script generation was successful
    if (!scriptResult.success) {
      throw new Error(
        `Script generation failed: ${(scriptResult as any).error}`
      )
    }

    console.log('🎉 Script Generation Agent completed successfully!')

    // ============================================================================
    // STEP 2: FUTURE AGENTS (PLACEHOLDER)
    // ============================================================================

    // ============================================================================
    // FINAL RESULTS
    // ============================================================================

    const finalResult = {
      success: true,
      mode: 'agentic-ai',
      timestamp: new Date().toISOString(),

      // Agent Results
      agents: {
        scriptGeneration: scriptResult,
      },

      // Generated Content
      script: (scriptResult as any).script,

      // Metadata
      metadata: {
        userId,
        organizationId,
        inputType, // Track which input type was used
        originalInput: userIdea, // The actual input content
        parameters: {
          duration,
          tone,
          audience,
          platform,
          language,
          keywords,
          hook,
          callToAction,
        },
      },

      // Performance Metrics
      metrics: {
        totalExecutionTime: (scriptResult as any).executionTime,
        agentsUsed: 1,
        agentsSuccessful: 1,
        agentsFailed: 0,
      },
    }

    throw new Error(`Script generation failed: ${(scriptResult as any).error}`)

    return finalResult
  }
)
