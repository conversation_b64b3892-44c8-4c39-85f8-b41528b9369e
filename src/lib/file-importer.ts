export interface FileImportRequest {
  sourceUrl: string
  userId?: string
  filename?: string
  folder?: string
}

export interface FileImportResponse {
  success: boolean
  url?: string
  path?: string
  filename?: string
  size?: number
  error?: string
}

export interface FileImportProgress {
  stage: 'starting' | 'downloading' | 'uploading' | 'complete' | 'error'
  message: string
  progress?: number
}

/**
 * Import a file from an external URL using Supabase Edge Function
 * This bypasses Vercel's timeout and memory limits for large files
 */
export async function importFileFromUrl(
  request: FileImportRequest,
  onProgress?: (progress: FileImportProgress) => void
): Promise<FileImportResponse> {
  try {
    onProgress?.({
      stage: 'starting',
      message: 'Initializing file import...',
      progress: 0,
    })

    // Get the Supabase URL for the Edge Function
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    if (!supabaseUrl) {
      throw new Error('Supabase URL not configured')
    }

    const functionUrl = `${supabaseUrl}/functions/v1/file-importer`

    onProgress?.({
      stage: 'downloading',
      message: 'Downloading file from source...',
      progress: 25,
    })

    // Call the Edge Function
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      )
    }

    onProgress?.({
      stage: 'uploading',
      message: 'Uploading to storage...',
      progress: 75,
    })

    const result: FileImportResponse = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'File import failed')
    }

    onProgress?.({
      stage: 'complete',
      message: 'File imported successfully!',
      progress: 100,
    })

    return result
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error'

    onProgress?.({
      stage: 'error',
      message: `Import failed: ${errorMessage}`,
      progress: 0,
    })

    return {
      success: false,
      error: errorMessage,
    }
  }
}

/**
 * Import a podcast episode from its enclosure URL
 * Convenience wrapper for podcast-specific imports
 */
export async function importPodcastEpisode(
  episodeUrl: string,
  episodeTitle: string,
  userId: string,
  onProgress?: (progress: FileImportProgress) => void
): Promise<FileImportResponse> {
  // Generate a clean filename from the episode title
  const cleanTitle = episodeTitle
    .replace(/[^a-zA-Z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .toLowerCase()
    .substring(0, 100) // Limit length

  const filename = `${cleanTitle}.mp3`

  return importFileFromUrl(
    {
      sourceUrl: episodeUrl,
      userId,
      filename,
      folder: 'podcasts',
    },
    onProgress
  )
}

/**
 * Check if a URL is likely to be a large file that should use the Edge Function
 * This is a heuristic based on common podcast hosting patterns
 */
export function shouldUseEdgeFunction(url: string): boolean {
  try {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname.toLowerCase()

    // Common podcast hosting services that often have large files
    const podcastHosts = [
      'anchor.fm',
      'soundcloud.com',
      'libsyn.com',
      'buzzsprout.com',
      'podbean.com',
      'spreaker.com',
      'simplecast.com',
      'transistor.fm',
      'castos.com',
      'blubrry.com',
    ]

    // Check if it's a known podcast host
    const isPodcastHost = podcastHosts.some(
      host => hostname.includes(host) || hostname.endsWith(host)
    )

    // Check if the URL suggests it's an audio file
    const isAudioFile = /\.(mp3|m4a|wav|ogg|aac)(\?|$)/i.test(url)

    return isPodcastHost || isAudioFile
  } catch {
    // If URL parsing fails, default to using Edge Function for safety
    return true
  }
}
