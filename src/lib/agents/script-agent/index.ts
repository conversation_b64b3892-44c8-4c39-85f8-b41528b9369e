/**
 * Script Generation Agent
 *
 * This is the main entry point for the script generation agent.
 * It creates and compiles the LangGraph workflow that handles
 * all four input types: text ideas, blog URLs, PDF URLs, and plain text.
 */

import { StateGraph, START, END } from '@langchain/langgraph'
import {
  ScriptState,
  type ScriptStateType,
  type ScriptGenerationParams,
} from './state'
import { inputDetectionNode } from './nodes/input-detection'
import { blogParsingNode } from './nodes/blog-parsing'
import { pdfParsingNode } from './nodes/pdf-parsing'
import { textParsingNode } from './nodes/text-parsing'
import { generateScriptNode } from './nodes/script-generation'
import { scriptReasoningNode } from './nodes/script-reasoning'
import { scriptCritiqueNode, critiqueRouter } from './nodes/script-critique'
import { scriptMetaPromptingNode } from './nodes/script-meta-prompting'
import { scriptRefinementNode } from './nodes/script-refinement'

/**
 * Creates and compiles the script generation agent workflow
 *
 * @param enableReActLoop - Whether to enable the ReAct loop for iterative improvement
 * @returns Compiled LangGraph workflow
 */
export function createScriptAgent(enableReActLoop: boolean = true) {
  if (enableReActLoop) {
    return createReActScriptAgent()
  } else {
    return createBasicScriptAgent()
  }
}

/**
 * Creates the basic script generation workflow (original linear workflow)
 *
 * Workflow Structure:
 * START → input_detection → [parsing] → generate_script → END
 */
function createBasicScriptAgent() {
  const workflow = new StateGraph(ScriptState)
    // Add basic processing nodes
    .addNode('input_detection', inputDetectionNode)
    .addNode('blog_parsing', blogParsingNode)
    .addNode('pdf_parsing', pdfParsingNode)
    .addNode('text_parsing', textParsingNode)
    .addNode('generate_script', generateScriptNode)

    // Define the entry point
    .addEdge(START, 'input_detection')

    // Conditional routing based on input type
    .addConditionalEdges(
      'input_detection',
      // Router function: decides which node to go to next
      (state: ScriptStateType) => {
        switch (state.inputType) {
          case 'blog_url':
            return 'blog_parsing'
          case 'pdf_url':
            return 'pdf_parsing'
          case 'text':
            return 'text_parsing'
          case 'idea':
          default:
            return 'generate_script'
        }
      },
      // Mapping of router outputs to node names
      {
        blog_parsing: 'blog_parsing',
        pdf_parsing: 'pdf_parsing',
        text_parsing: 'text_parsing',
        generate_script: 'generate_script',
      }
    )

    // Connect all parsing nodes to script generation
    .addEdge('blog_parsing', 'generate_script')
    .addEdge('pdf_parsing', 'generate_script')
    .addEdge('text_parsing', 'generate_script')
    .addEdge('generate_script', END)

  // Compile the graph into an executable workflow
  return workflow.compile()
}

/**
 * Creates the ReAct script generation workflow with iterative improvement
 *
 * Workflow Structure:
 * START → input_detection → [parsing] → generate_script → script_reasoning → script_critique
 *                                                                                ↓
 *                                                                    [Quality Check Decision]
 *                                                                    ↙                    ↘
 *                                                            (Good enough)          (Needs improvement)
 *                                                                ↓                        ↓
 *                                                              END                script_refinement
 *                                                                                        ↓
 *                                                                                script_reasoning (loop back)
 */
function createReActScriptAgent() {
  const workflow = new StateGraph(ScriptState)
    // Add all processing nodes
    .addNode('input_detection', inputDetectionNode)
    .addNode('blog_parsing', blogParsingNode)
    .addNode('pdf_parsing', pdfParsingNode)
    .addNode('text_parsing', textParsingNode)
    .addNode('generate_script', generateScriptNode)

    // Add ReAct loop nodes
    .addNode('script_reasoning', scriptReasoningNode)
    .addNode('script_critique', scriptCritiqueNode)
    .addNode('script_meta_prompting', scriptMetaPromptingNode)
    .addNode('script_refinement', scriptRefinementNode)

    // Define the entry point
    .addEdge(START, 'input_detection')

    // Conditional routing based on input type
    .addConditionalEdges(
      'input_detection',
      // Router function: decides which node to go to next
      (state: ScriptStateType) => {
        switch (state.inputType) {
          case 'blog_url':
            return 'blog_parsing'
          case 'pdf_url':
            return 'pdf_parsing'
          case 'text':
            return 'text_parsing'
          case 'idea':
          default:
            return 'generate_script'
        }
      },
      // Mapping of router outputs to node names
      {
        blog_parsing: 'blog_parsing',
        pdf_parsing: 'pdf_parsing',
        text_parsing: 'text_parsing',
        generate_script: 'generate_script',
      }
    )

    // Connect all parsing nodes to script generation
    .addEdge('blog_parsing', 'generate_script')
    .addEdge('pdf_parsing', 'generate_script')
    .addEdge('text_parsing', 'generate_script')

    // Connect script generation to ReAct loop
    .addEdge('generate_script', 'script_reasoning')

    // ReAct loop: reasoning → critique → decision
    .addEdge('script_reasoning', 'script_critique')

    // Conditional routing after critique
    .addConditionalEdges(
      'script_critique',
      // Router function: decides whether to continue iterating or finish
      critiqueRouter,
      // Mapping of router outputs to node names
      {
        script_meta_prompting: 'script_meta_prompting',
        END: END,
      }
    )

    // Connect meta-prompting to refinement
    .addEdge('script_meta_prompting', 'script_refinement')

    // Connect refinement back to reasoning for next iteration
    .addEdge('script_refinement', 'script_reasoning')

  // Compile the graph into an executable workflow
  return workflow.compile()
}

/**
 * Main function to generate video scripts using the agent
 *
 * This is the primary interface for script generation. It accepts
 * user parameters, creates the agent workflow, and returns the generated script.
 *
 * @param params - Script generation parameters
 * @returns Promise resolving to the generated script
 */
export async function generateVideoScript(params: ScriptGenerationParams) {
  // Create the agent workflow with optional ReAct loop
  const enableReActLoop = params.enableReActLoop !== false // Default to true
  const agent = createScriptAgent(enableReActLoop)

  // Prepare initial state
  const initialState: Partial<ScriptStateType> = {
    // User input and parameters
    userIdea: params.userIdea,
    duration: params.duration,
    tone: params.tone,
    audience: params.audience,
    platform: params.platform,
    language: params.language,
    keywords: params.keywords || null,
    hook: params.hook || false,
    callToAction: params.callToAction || false,

    // Initialize content parsing fields
    inputType: 'idea' as const, // Will be detected by input_detection node
    blogContent: null,
    blogSummary: null,
    pdfContent: null,
    pdfSummary: null,
    textContent: null,
    textSummary: null,
    extractedKeywords: null,

    // Initialize ReAct loop fields
    iterationCount: 0,
    maxIterations: params.maxIterations || 2, // Default to 3 iterations
    qualityScore: null,
    qualityThreshold: params.qualityThreshold || 8, // Default threshold of 7/10
    reasoningHistory: [],

    // Initialize processing state
    status: 'processing' as const,
    error: null,
  }

  // Execute the workflow
  const result = await agent.invoke(initialState)

  // Handle workflow results
  if (result.status === 'failed') {
    throw new Error(result.error || 'Script generation failed')
  }

  if (!result.script) {
    throw new Error('No script was generated')
  }

  return result.script
}

// Re-export types and utilities for external use
export type {
  ScriptStateType,
  ScriptGenerationParams,
  GeneratedScript,
} from './state'
export { ScriptState } from './state'
