/**
 * Script Audio Generation Node
 *
 * This node generates professional voiceover audio for each scene using ElevenLabs API.
 * It processes scenes with controlled concurrency, handles caching, and uploads to Supabase.
 */

import type { ScriptStateType } from '../state'
import { createClient } from '@supabase/supabase-js'
import fetch from 'node-fetch'
import FormData from 'form-data'
import * as hash from 'object-hash'
import fs from 'fs'
import ffprobe from 'ffprobe'
import ffprobeStatic from 'ffprobe-static'

// Supabase client for audio uploads
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface SceneAudioResult {
  sceneNumber: number
  audioUrl: string
  audioDuration: number
  alignment: {
    characters: string[]
    character_start_times_seconds: number[]
    character_end_times_seconds: number[]
  }
}

/**
 * Script Audio Generation Node - Generates professional voiceover for all scenes
 */
export async function scriptAudioGenerationNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  if (!state.script) {
    return {
      status: 'failed' as const,
      error: 'No script available for audio generation',
    }
  }

  try {
    console.log(
      '🎵 Starting audio generation for',
      state.script.scenes.length,
      'scenes'
    )

    // Process scenes with concurrency limit (ElevenLabs rate limit: 3 concurrent)
    const audioResults = await processWithConcurrencyLimit(
      state.script.scenes,
      async scene => {
        return await generateSceneAudio(
          scene.text,
          scene.sceneNumber,
          state.voice || { voice_id: 'pNInz6obpgDQGcFmaJgB', name: 'Adam' }, // Default voice
          state.userId || 'anonymous'
        )
      },
      2 // ElevenLabs concurrency limit
    )

    // Filter out failed generations
    const successfulResults = audioResults.filter(
      result => result !== null
    ) as SceneAudioResult[]

    if (successfulResults.length === 0) {
      return {
        status: 'failed' as const,
        error: 'All audio generation attempts failed',
      }
    }

    console.log(
      `✅ Audio generation completed: ${successfulResults.length}/${state.script.scenes.length} scenes`
    )

    return {
      audioResults: successfulResults,
      audioGenerationStatus: 'completed' as const,
    }
  } catch (error) {
    console.error('❌ Audio generation failed:', error)
    return {
      status: 'failed' as const,
      error: `Audio generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      audioGenerationStatus: 'failed' as const,
    }
  }
}

/**
 * Process array with concurrency limit
 */
async function processWithConcurrencyLimit<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  limit: number
): Promise<R[]> {
  const results: R[] = []

  for (let i = 0; i < items.length; i += limit) {
    const batch = items.slice(i, i + limit)
    const batchResults = await Promise.all(batch.map(processor))
    results.push(...batchResults)
  }

  return results
}

/**
 * Generate audio for a single scene using ElevenLabs
 */
async function generateSceneAudio(
  text: string,
  sceneNumber: number,
  voice: { voice_id: string; name?: string },
  userId: string
): Promise<SceneAudioResult | null> {
  try {
    const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY
    if (!ELEVEN_API_KEY) {
      console.error('ElevenLabs API key not configured')
      return null
    }

    // Create request hash for caching
    const requestHash = hash.MD5({ text, voiceId: voice.voice_id })
    console.log(
      `🎵 Generating audio for scene ${sceneNumber} (hash: ${requestHash})`
    )

    // Check for cached audio
    const bucket = 'assets'
    const filePath = `${userId}/tts/${requestHash}.mp3`
    const SUPABASE_URL =
      process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
    const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${filePath}`

    // Check if cached file exists
    const storageRes = await fetch(publicUrl)
    if (storageRes.ok) {
      console.log(`✅ Found cached audio for scene ${sceneNumber}`)

      // Get forced alignment for cached file
      const alignment = await getForcedAlignment(publicUrl, text)
      if (alignment) {
        const audioDuration =
          alignment.character_end_times_seconds.length > 0
            ? alignment.character_end_times_seconds[
                alignment.character_end_times_seconds.length - 1
              ]
            : 10

        return {
          sceneNumber,
          audioUrl: publicUrl,
          audioDuration,
          alignment,
        }
      }
    }

    // Generate new audio with streaming timestamps
    console.log(`🎵 Generating new audio for scene ${sceneNumber}`)
    const url = `https://api.elevenlabs.io/v1/text-to-speech/${voice.voice_id}/stream/with-timestamps`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xi-api-key': ELEVEN_API_KEY,
      },
      body: JSON.stringify({
        text,
        model_id: 'eleven_multilingual_v2',
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.75,
        },
      }),
    })

    if (!response.ok) {
      console.error(
        `ElevenLabs API error for scene ${sceneNumber}:`,
        response.status
      )
      return null
    }

    // Process streaming response
    let audioBytes = Buffer.from('')
    let characters: string[] = []
    let characterStartTimesSeconds: number[] = []
    let characterEndTimesSeconds: number[] = []
    let buffer = ''

    const nodeStream = response.body as unknown as NodeJS.ReadableStream
    if (!nodeStream) {
      throw new Error('Failed to get response stream')
    }

    for await (const chunk of nodeStream) {
      buffer += chunk.toString()
      const lines = buffer.split('\n')
      buffer = lines.pop()!

      for (const line of lines) {
        if (!line.trim()) continue
        try {
          const responseDict = JSON.parse(line)

          if (responseDict.audio_base64) {
            const audioBytesChunk = Buffer.from(
              responseDict.audio_base64,
              'base64'
            )
            audioBytes = Buffer.concat([audioBytes, audioBytesChunk])
          }

          if (responseDict.alignment) {
            characters = characters.concat(responseDict.alignment.characters)
            characterStartTimesSeconds = characterStartTimesSeconds.concat(
              responseDict.alignment.character_start_times_seconds
            )
            characterEndTimesSeconds = characterEndTimesSeconds.concat(
              responseDict.alignment.character_end_times_seconds
            )
          }
        } catch (e) {
          console.error('JSON parsing error:', e, 'Line:', line)
        }
      }
    }

    if (audioBytes.length === 0) {
      throw new Error('No audio data received from ElevenLabs')
    }

    // Upload to Supabase
    await uploadAudioBufferToSupabase(audioBytes, filePath, bucket)

    const alignment = {
      characters,
      character_start_times_seconds: characterStartTimesSeconds,
      character_end_times_seconds: characterEndTimesSeconds,
    }

    const audioDuration =
      characterEndTimesSeconds.length > 0
        ? characterEndTimesSeconds[characterEndTimesSeconds.length - 1]
        : await getAudioDuration(audioBytes)

    console.log(
      `✅ Audio generated for scene ${sceneNumber} (${audioDuration}s)`
    )

    return {
      sceneNumber,
      audioUrl: publicUrl,
      audioDuration,
      alignment,
    }
  } catch (error) {
    console.error(`❌ Audio generation failed for scene ${sceneNumber}:`, error)
    return null
  }
}

/**
 * Upload audio buffer to Supabase storage
 */
async function uploadAudioBufferToSupabase(
  audioBuffer: Buffer,
  filePath: string,
  bucket: string
): Promise<void> {
  const { error: uploadError } = await supabase.storage
    .from(bucket)
    .upload(filePath, audioBuffer, {
      contentType: 'audio/mpeg',
      upsert: false,
    })

  if (uploadError) {
    console.error('Supabase audio upload error:', uploadError)
    throw new Error(`Upload failed: ${uploadError.message}`)
  }
}

/**
 * Get forced alignment from ElevenLabs
 */
async function getForcedAlignment(
  audioUrl: string,
  text: string
): Promise<{
  characters: string[]
  character_start_times_seconds: number[]
  character_end_times_seconds: number[]
} | null> {
  try {
    const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY
    if (!ELEVEN_API_KEY) return null

    const audioResponse = await fetch(audioUrl)
    if (!audioResponse.ok) return null

    const audioBuffer = Buffer.from(await audioResponse.arrayBuffer())
    const form: FormData = new FormData()
    form.append('file', audioBuffer, {
      filename: 'audio.mp3',
      contentType: 'audio/mpeg',
    })
    form.append('text', text)

    const response = await fetch(
      'https://api.elevenlabs.io/v1/forced-alignment',
      {
        method: 'POST',
        headers: {
          'xi-api-key': ELEVEN_API_KEY,
          ...form.getHeaders(),
        },
        body: form,
      }
    )

    if (!response.ok) return null

    const data = (await response.json()) as {
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    }

    return data
  } catch (error) {
    console.error('Forced alignment error:', error)
    return null
  }
}

/**
 * Get audio duration using ffprobe
 */
async function getAudioDuration(audioBuffer: Buffer): Promise<number> {
  try {
    const tempPath = `/tmp/audio-${Date.now()}.mp3`
    fs.writeFileSync(tempPath, audioBuffer)

    try {
      const info = (await (
        ffprobe as (path: string, opts: unknown) => Promise<unknown>
      )(tempPath, { path: ffprobeStatic.path })) as {
        streams: { duration?: string }[]
      }

      const duration = info.streams[0]?.duration
      return duration ? parseFloat(duration) : 10
    } finally {
      fs.unlinkSync(tempPath)
    }
  } catch (error) {
    console.warn('Failed to get audio duration:', error)
    return 10 // fallback duration
  }
}
