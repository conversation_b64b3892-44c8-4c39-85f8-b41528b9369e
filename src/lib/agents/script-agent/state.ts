/**
 * Script Agent State Definition
 *
 * This file defines the state schema for the script generation agent.
 * The state flows through all nodes in the workflow, allowing each node to
 * read data from previous steps and add new data for future steps.
 */

import { Annotation } from '@langchain/langgraph'
import { HumanMessage, AIMessage } from '@langchain/core/messages'

/**
 * Script Agent State Schema
 *
 * In LangGraph, the "state" is a shared data structure that flows through
 * all nodes in the workflow. Think of it as a "context" that gets passed
 * from one step to the next, allowing each node to:
 * 1. Read data from previous steps
 * 2. Add new data for future steps
 * 3. Update existing data
 *
 * State flows like this: Initial State → Node 1 → Updated State → Node 2 → Final State
 */
export const ScriptState = Annotation.Root({
  // INPUT FIELDS - Data provided by the user/caller
  userIdea: Annotation<string>, // The video idea from the user (could be text idea, blog URL, PDF URL, or plain text)
  duration: Annotation<number>, // Target video duration in seconds

  // CONTENT PARAMETERS - How the script should be crafted
  tone: Annotation<string>, // Writing tone: 'friendly', 'professional', 'energetic', etc.
  audience: Annotation<string>, // Target audience: 'general', 'professionals', 'students', etc.
  platform: Annotation<string>, // Platform: 'YouTube', 'TikTok', 'Instagram', 'LinkedIn', etc.
  language: Annotation<string>, // Language: 'English', 'Spanish', 'French', etc.
  keywords: Annotation<string | null>, // Optional keywords to include
  hook: Annotation<boolean>, // Whether to include attention-grabbing opening
  callToAction: Annotation<boolean>, // Whether to include platform-specific CTA

  // CONTENT PARSING FIELDS - For different input types
  inputType: Annotation<'idea' | 'blog_url' | 'pdf_url' | 'text'>, // Detected input type
  blogContent: Annotation<string | null>, // Extracted blog content (markdown)
  blogSummary: Annotation<string | null>, // LLM-generated summary of blog content
  pdfContent: Annotation<string | null>, // Extracted PDF content (markdown)
  pdfSummary: Annotation<string | null>, // LLM-generated summary of PDF content
  textContent: Annotation<string | null>, // Raw text content
  textSummary: Annotation<string | null>, // LLM-generated summary of text content
  extractedKeywords: Annotation<string[] | null>, // Keywords extracted from content

  // PROCESSING FIELDS - Data used during agent execution
  messages: Annotation<(HumanMessage | AIMessage)[]>({
    // Reducer function: defines how new messages are merged with existing ones
    // In this case, we concatenate new messages to the existing array
    reducer: (x, y) => x.concat(y),
    default: () => [], // Start with empty message array
  }),

  // OUTPUT FIELDS - Data generated by the agent
  script: Annotation<{
    title: string
    scenes: Array<{
      id: string
      sceneNumber: number
      text: string
      duration: number
      searchKeywords: string[]
    }>
  } | null>, // The generated video script

  // REACT LOOP FIELDS - For iterative script improvement
  iterationCount: Annotation<number>({
    // Reducer: always use the latest count (replace, don't merge)
    reducer: (x, y) => y,
    default: () => 0, // Start with 0 iterations
  }),
  maxIterations: Annotation<number>({
    // Reducer: always use the latest max (replace, don't merge)
    reducer: (x, y) => y,
    default: () => 3, // Default maximum of 3 refinement iterations
  }),
  qualityScore: Annotation<number | null>, // Current script quality score (1-10)
  qualityThreshold: Annotation<number>({
    // Reducer: always use the latest threshold (replace, don't merge)
    reducer: (x, y) => y,
    default: () => 8, // Default minimum acceptable score of 8/10
  }),
  reasoningHistory: Annotation<
    Array<{
      iteration: number
      reasoning: string
      suggestions: string[]
      qualityScore: number
      improvements: string[]
    }>
  >({
    // Reducer: concatenate new reasoning entries to existing array
    reducer: (x, y) => x.concat(y),
    default: () => [], // Start with empty reasoning history
  }),

  // META-PROMPTING - For autonomous improvement strategy generation
  metaPrompt: Annotation<string | null>, // Generated custom refinement prompt

  // STATUS TRACKING - Monitor the agent's progress
  status: Annotation<'processing' | 'completed' | 'failed'>({
    // Reducer: always use the latest status (replace, don't merge)
    reducer: (x, y) => y,
    default: () => 'processing', // Start in processing state
  }),
  error: Annotation<string | null>, // Error message if something goes wrong
})

/**
 * Type alias for the script state
 * This makes it easier to use the state type in function signatures
 */
export type ScriptStateType = typeof ScriptState.State

/**
 * Input parameters for the script generation agent
 */
export interface ScriptGenerationParams {
  userIdea: string
  duration: number
  tone: string
  audience: string
  platform: string
  language: string
  keywords?: string | null
  hook?: boolean
  callToAction?: boolean
  // ReAct loop configuration
  enableReActLoop?: boolean // Whether to enable iterative improvement (default: true)
  maxIterations?: number // Maximum refinement iterations (default: 3)
  qualityThreshold?: number // Minimum quality score to stop iterating (default: 8)
}

/**
 * Generated script structure
 */
export interface GeneratedScript {
  title: string
  scenes: Array<{
    id: string
    sceneNumber: number
    text: string
    duration: number
    searchKeywords: string[]
  }>
}

/**
 * Content analysis result structure (used by parsing nodes)
 */
export interface ContentAnalysis {
  summary: string
  keyPoints: string[]
  extractedKeywords: string[]
  suggestedTitle: string
  mainTheme: string
  targetAudience: string
  mentionedCompanyOrProduct: {
    name: string | null
    role: string | null
    callToAction: string | null
  }
}

/**
 * Script quality evaluation criteria
 */
export interface QualityCriteria {
  engagement: number // Hook quality, compelling narrative flow (1-10)
  clarity: number // Clear message, easy to understand (1-10)
  platformOptimization: number // Appropriate for target platform (1-10)
  toneConsistency: number // Maintains requested tone throughout (1-10)
  durationAccuracy: number // Total duration matches target (1-10)
  characterLimits: number // Each scene respects 200 character maximum (1-10)
  keywordIntegration: number // Natural incorporation of keywords (1-10)
  ctaHookImplementation: number // Proper CTA/Hook implementation (1-10)
}

/**
 * Script reasoning result structure
 */
export interface ScriptReasoning {
  overallAssessment: string
  qualityCriteria: QualityCriteria
  overallScore: number
  specificIssues: string[]
  improvementSuggestions: string[]
  shouldContinueIterating: boolean
}

/**
 * Script refinement result structure
 */
export interface ScriptRefinement {
  improvedScript: GeneratedScript
  changesApplied: string[]
  reasoningApplied: string[]
}
