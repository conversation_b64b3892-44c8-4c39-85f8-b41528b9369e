import { NextRequest, NextResponse } from 'next/server'
import { getUserSession } from '@/lib/user-utils'

interface StatusResponse {
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  output?: any
  error?: string
  progress?: {
    step: string
    message: string
    percentage?: number
  }
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate user
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(req.url)
    const eventId = searchParams.get('eventId')

    if (!eventId) {
      return NextResponse.json(
        { error: 'Missing eventId parameter' },
        { status: 400 }
      )
    }

    console.log('📊 Status check requested for eventId:', eventId)
    
    // For now, return a simple running status
    // The Inngest function will complete and the frontend will handle timeout
    // In production, you would implement proper status checking via webhooks or database
    return NextResponse.json({
      status: 'running',
      progress: {
        step: 'processing',
        message: 'Processing podcast download...',
        percentage: 50
      }
    } as StatusResponse)

  } catch (error) {
    console.error('❌ Status check error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 'failed'
      },
      { status: 500 }
    )
  }
}
