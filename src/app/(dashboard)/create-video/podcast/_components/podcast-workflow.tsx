'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  ArrowLeft,
  Clock,
  Calendar,
  Loader2,
  Sparkles,
  CheckCircle,
  Gem,
} from 'lucide-react'
import { authClient } from '@/lib/auth-client'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useRouter } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'
import { useVideoStore } from '@/store/video-store'
import { toast } from '@/lib/toast'
import { useDefaultAutopickValue } from '@/hooks/use-gated-autopick-options'
import {
  usePodcastDurationLimits,
  useCanCreateProject,
} from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import posthog from 'posthog-js'
import {
  importPodcastEpisode,
  shouldUseEdgeFunction,
  type FileImportProgress,
} from '@/lib/file-importer'

import { PodcastSearch } from './podcast-search'
import { EpisodeSelection } from './episode-selection'
import { PodcastVideoConfig } from '@/app/(dashboard)/_components/video-form'
import { PodcastAudioPlayer } from './podcast-audio-player'
import type { Podcast } from '@/hooks/usePodcastSearch'
import type { Episode } from '@/hooks/usePodcastEpisodes'
import type { AutomationRequest } from '@/lib/types'

interface VideoConfig {
  orientation: 'landscape' | 'portrait' | 'square'
  autopick: string
}

// We'll initialize this inside the component to use the hook

export function PodcastWorkflow() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { data: session } = authClient.useSession()
  const { defaultValue: defaultAutopick } = useDefaultAutopickValue()
  const podcastLimits = usePodcastDurationLimits()
  const { openUpgradeModal } = useUpgradeModal()
  const projectAccess = useCanCreateProject()

  console.log('Project access:', projectAccess)

  // Track video creation started when component mounts (only once)
  const hasTracked = React.useRef(false)
  useEffect(() => {
    if (!hasTracked.current) {
      posthog.capture('video_creation_started', {
        method: 'podcast_to_video',
        source: 'create_video_page',
      })
      hasTracked.current = true
    }
  }, [])

  // Initialize config with default autopick value
  const initialConfig: VideoConfig = {
    orientation: 'landscape',
    autopick: defaultAutopick,
  }

  const [selectedPodcast, setSelectedPodcast] = useState<Podcast | null>(null)
  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null)
  const [config, setConfig] = useState<VideoConfig>(initialConfig)
  const [isGenerating, setIsGenerating] = useState(false)
  const [eventId, setEventId] = useState<string | null>(null)
  const [progressMessage, setProgressMessage] = useState('')
  const { status, output } = useInngestRunStatus(eventId)
  const [clipPace, setClipPace] = useState<
    'fast' | 'medium' | 'slow' | 'verySlow'
  >('medium')
  const [actualDuration, setActualDuration] = useState<number | null>(null)
  const isAudioLoaded = actualDuration !== null

  // File import state
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] =
    useState<FileImportProgress | null>(null)

  // Reset detected duration and import state when user picks a different episode
  useEffect(() => {
    setActualDuration(null)
    setImportProgress(null)
  }, [selectedEpisode?.id])

  // Check if selected episode exceeds duration limits
  const episodeDurationSeconds =
    actualDuration || selectedEpisode?.duration || 0
  const canProcessEpisode = podcastLimits.canProcessEpisode(
    episodeDurationSeconds
  )
  const episodeDurationMinutes = Math.floor(episodeDurationSeconds / 60)

  // Gated handler that checks both project and duration limits
  const handleGatedGenerateVideo = () => {
    if (!selectedEpisode) {
      toast.error('Please select an episode to generate video from')
      return
    }

    // Check project limits first
    if (!projectAccess.canCreate) {
      openUpgradeModal('projects', projectAccess.upgradeMessage)
      return
    }

    // Check duration limits
    if (!canProcessEpisode) {
      const upgradeMessage = `This episode is ${episodeDurationMinutes} minutes long, but your ${podcastLimits.planName} plan allows episodes up to ${podcastLimits.maxDurationMinutes} minutes. Upgrade to process longer episodes.`
      openUpgradeModal('podcastDuration', upgradeMessage)
      return
    }

    // If within limits, proceed with normal generation
    handleGenerateVideo()
  }

  const handleGenerateVideo = async () => {
    if (!selectedEpisode) {
      toast.error('Please select an episode to generate video from')
      return
    }

    if (!session?.user?.id) {
      toast.error('You must be logged in to generate videos')
      return
    }

    // Track video creation flow started
    posthog.capture('video_creation_flow_started', {
      method: 'podcast_to_video',
      source: 'generate_button',
    })

    setIsGenerating(true)

    try {
      let podcastUrl = selectedEpisode.enclosureUrl

      console.log('Podcast URL:', podcastUrl)
      console.log(
        'Using Edge Function:',
        shouldUseEdgeFunction(selectedEpisode.enclosureUrl)
      )

      setIsGenerating(false)
      return

      // Check if we should use the Edge Function for large files
      if (shouldUseEdgeFunction(selectedEpisode.enclosureUrl)) {
        console.log('🔄 Using Edge Function for large file import')
        setIsImporting(true)

        const importResult = await importPodcastEpisode(
          selectedEpisode.enclosureUrl,
          selectedEpisode.title,
          session.user.id,
          progress => {
            setImportProgress(progress)
            setProgressMessage(progress.message)
          }
        )

        setIsImporting(false)

        if (!importResult.success) {
          throw new Error(importResult.error || 'Failed to import podcast file')
        }

        // Use the imported file URL instead of the original
        podcastUrl = importResult.url!
        console.log('✅ File imported successfully:', podcastUrl)
      }

      const automationRequest: AutomationRequest = {
        podcastUrl,
        episodeTitle: selectedEpisode.title,
        episodeDescription: selectedEpisode.description,
        episodeDuration: actualDuration || selectedEpisode.duration,
        method: 'Podcast to Video',
        idea: '',
        tone: 'friendly',
        audience: 'general',
        platform: 'youtube',
        hook: false,
        callToAction: false,
        // Use the detected actual duration for downstream pipeline
        duration: actualDuration || selectedEpisode.duration,
        language: 'english',
        orientation: config.orientation,
        autopick: config.autopick,
        voice: null,
        userId: session?.user?.id,
        organizationId: session?.session?.activeOrganizationId || undefined,
        clipPace,
      }
      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })

      if (!response.ok) {
        throw new Error('Failed to generate video')
      }

      const data = await response.json()
      setEventId(data.eventId)
      setProgressMessage('🎬 Starting video generation...')
    } catch (error) {
      console.error('Error generating video:', error)
      toast.error('Failed to generate video. Please try again.')
      setIsGenerating(false)
    }
  }

  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output &&
      typeof output.projectId === 'string'
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any // API response will be transformed by setProjectData
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled') {
      setProgressMessage('')
      toast.error('Video generation failed. Please try again.')
      setIsGenerating(false)
    }
  }, [status, output, queryClient, router])

  const handleBackToSearch = () => {
    setSelectedPodcast(null)
    setSelectedEpisode(null)
  }

  const handleBackToEpisodes = () => {
    setSelectedEpisode(null)
  }

  const formatDuration = (seconds: number) => {
    const totalSeconds = Math.floor(seconds || 0)
    const minutes = Math.floor(totalSeconds / 60)
    const remainingSeconds = totalSeconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  return (
    <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
      {/* Step 1: Podcast Selection */}
      {!selectedPodcast && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <PodcastSearch onPodcastSelect={setSelectedPodcast} />
        </motion.div>
      )}

      {/* Step 2: Episode Selection */}
      <AnimatePresence>
        {selectedPodcast && !selectedEpisode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <EpisodeSelection
              podcast={selectedPodcast}
              onEpisodeSelect={setSelectedEpisode}
              onBack={handleBackToSearch}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Step 3: Video Configuration */}
      <AnimatePresence>
        {selectedPodcast && selectedEpisode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className='space-y-4'
          >
            {/* Selected Episode Summary Card */}
            <Card className='gap-1'>
              <CardHeader className='pb-3'>
                <div className='flex items-center gap-2'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleBackToEpisodes}
                    className='h-8 px-2'
                  >
                    <ArrowLeft className='w-4 h-4 mr-1' />
                    Change Episode
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='pt-0'>
                <div className='flex items-start gap-3'>
                  <img
                    src={selectedEpisode.image || selectedPodcast.image}
                    alt={selectedEpisode.title}
                    className='w-16 h-16 rounded-lg object-cover flex-shrink-0'
                  />
                  <div className='flex-1 min-w-0'>
                    <h3 className='font-medium text-base leading-tight line-clamp-2 mb-2'>
                      {selectedEpisode.title}
                    </h3>
                    <p className='text-sm text-muted-foreground mb-2'>
                      {selectedPodcast.title}
                    </p>
                    <div className='flex items-center gap-4 text-xs text-muted-foreground mb-3'>
                      <div className='flex items-center gap-1'>
                        <Calendar className='w-3 h-3' />
                        {formatDate(selectedEpisode.datePublished)}
                      </div>
                      <div className='flex items-center gap-1'>
                        <Clock className='w-3 h-3' />
                        <span>
                          {actualDuration
                            ? formatDuration(actualDuration)
                            : formatDuration(selectedEpisode.duration)}
                        </span>
                      </div>
                    </div>

                    {/* Audio Player */}
                    {selectedEpisode.enclosureUrl ? (
                      <PodcastAudioPlayer
                        audioUrl={selectedEpisode.enclosureUrl}
                        onDurationChange={setActualDuration}
                        className='mt-2'
                      />
                    ) : (
                      <div className='mt-2 p-3 bg-muted/50 rounded-lg text-center text-sm text-muted-foreground'>
                        Audio preview not available for this episode
                      </div>
                    )}
                  </div>
                </div>

                {/* Dynamic Ads Notice */}
                <p className='mt-3 text-xs border-t pt-3 text-orange-500'>
                  <strong>Note:</strong> Podcast episodes containing dynamic ads
                  cannot be removed during transcription. These ads will appear
                  in your generated video content. Please consider using audio
                  to video method instead.
                </p>
              </CardContent>
            </Card>

            {/* Video Configuration */}
            <PodcastVideoConfig
              orientation={config.orientation}
              onOrientationChange={value =>
                setConfig(prev => ({ ...prev, orientation: value }))
              }
              autopick={config.autopick}
              onAutopickChange={value =>
                setConfig(prev => ({ ...prev, autopick: value }))
              }
              clipPace={clipPace}
              onClipPaceChange={setClipPace}
              disabled={isGenerating}
              episodeDuration={actualDuration || selectedEpisode?.duration}
            />

            {/* Generate Button - Custom implementation for dual gating */}
            <div className='space-y-2 mt-6'>
              <Button
                onClick={handleGatedGenerateVideo}
                disabled={isGenerating || isImporting || !isAudioLoaded}
                className={`w-full h-10 text-sm ${
                  !projectAccess.canCreate || !canProcessEpisode
                    ? 'bg-gradient-to-r from-orange-600 to-orange-500 text-white hover:from-orange-700 hover:to-orange-600'
                    : ''
                }`}
                size='default'
              >
                {isImporting ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    {importProgress?.message || 'Importing podcast file...'}
                  </>
                ) : isGenerating ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    Creating Video...
                  </>
                ) : !projectAccess.canCreate || !canProcessEpisode ? (
                  <>
                    <Gem className='h-4 w-4 mr-2' />
                    {!projectAccess.canCreate
                      ? 'Upgrade to Create More Projects'
                      : `Episode too long - Upgrade for ${podcastLimits.maxDurationMinutes}+ min episodes`}
                  </>
                ) : (
                  <>
                    <Sparkles className='h-4 w-4 mr-2' />
                    Generate Video
                  </>
                )}
              </Button>

              {(!projectAccess.canCreate || !canProcessEpisode) && (
                <div className='text-center text-sm text-muted-foreground'>
                  {!projectAccess.canCreate
                    ? projectAccess.upgradeMessage
                    : `This episode is ${episodeDurationMinutes} minutes long, but your ${podcastLimits.planName} plan allows episodes up to ${podcastLimits.maxDurationMinutes} minutes.`}
                </div>
              )}

              {progressMessage.includes('complete! Redirecting') && (
                <div className='flex items-center justify-center gap-2 text-sm text-green-600'>
                  <CheckCircle className='h-4 w-4' />
                  <span>Redirecting to video editor...</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Loading Dialog */}
      <LoaderDialog
        open={isGenerating || isImporting}
        title={isImporting ? 'Importing Podcast File' : 'Creating Your Video'}
        subtitle={
          isImporting
            ? importProgress?.message || 'Importing your podcast file...'
            : progressMessage || 'Processing your podcast episode...'
        }
      />
    </div>
  )
}
