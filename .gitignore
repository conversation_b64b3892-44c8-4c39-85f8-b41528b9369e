# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
.vscode

# next.js
/.next/
/out/
/video-preview

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env
.env.prod
.env.test

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# inngest local db
.inngest/main.db

out/bundle

# Sql files
supabase/*
